<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firecracker Leles - Bay Area Evaluations</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827; /* gray-900 */
        }
        /* Custom focus style for better visibility */
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            border-color: #4f46e5; /* indigo-600 */
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.4);
        }
        .form-input, .form-select, .form-textarea {
            transition: all 0.2s ease-in-out;
        }
        /* Tab styles */
        .tab {
            transition: all 0.3s ease;
        }
        .tab.active {
            background-color: #f6e1bd; /* Custom color */
            color: #111827; /* gray-900 for contrast */
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        /* Animation for scroll */
        .reveal {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        /* Custom checkbox */
        .form-checkbox {
            appearance: none;
            background-color: #374151; /* gray-700 */
            border: 1px solid #4b5563; /* gray-600 */
            padding: 0.75rem;
            border-radius: 0.375rem;
            display: inline-block;
            position: relative;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .form-checkbox:checked {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }
        .form-checkbox:checked::after {
            content: '✔';
            position: absolute;
            color: white;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.8rem;
        }
        .card-content.disabled {
            opacity: 0.5;
            pointer-events: none;
        }
        /* Loading Screen Animation */
        .lineAnimation {
            animation: waveUp 1s ease-in-out infinite;
        }
        @keyframes waveUp {
            0%, 100% {
                transform: scaleY(0.2);
            }
            50% {
                transform: scaleY(1);
            }
        }
    </style>
</head>
<body class="text-gray-200">

    <div class="container mx-auto max-w-5xl p-4 sm:p-6 lg:p-8">
        
        <!-- Header -->
        <header class="text-center mb-8">
            <div class="w-24 h-24 mx-auto mb-4">
                <svg viewBox="0 0 100 100" class="w-full h-full">
                    <path d="M20,20 L80,20 L80,50 L50,70 L20,50 Z" fill="none" stroke="#f6e1bd" stroke-width="2"></path>
                    <line x1="20" y1="20" x2="80" y2="20" stroke="#f6e1bd" stroke-width="2"></line>
                    <text x="50" y="23" text-anchor="middle" font-size="9" font-weight="bold" fill="#f6e1bd">BASELINE</text>
                    <circle cx="20" cy="20" r="3" fill="#f6e1bd"></circle><circle cx="80" cy="20" r="3" fill="#f6e1bd"></circle>
                    <circle cx="20" cy="50" r="3" fill="#f6e1bd"></circle><circle cx="80" cy="50" r="3" fill="#f6e1bd"></circle>
                    <g><line x1="30" y1="26" x2="30" y2="51" stroke="#f6e1bd" stroke-width="2"></line><circle cx="30" cy="51" r="2.5" fill="#f6e1bd"></circle></g>
                    <g><line x1="40" y1="32" x2="40" y2="57" stroke="#f6e1bd" stroke-width="2"></line><circle cx="40" cy="57" r="2.5" fill="#f6e1bd"></circle></g>
                    <g><line x1="50" y1="36" x2="50" y2="61" stroke="#f6e1bd" stroke-width="2"></line><circle cx="50" cy="61" r="2.5" fill="#f6e1bd"></circle></g>
                    <g><line x1="60" y1="32" x2="60" y2="57" stroke="#f6e1bd" stroke-width="2"></line><circle cx="60" cy="57" r="2.5" fill="#f6e1bd"></circle></g>
                    <g><line x1="70" y1="26" x2="70" y2="51" stroke="#f6e1bd" stroke-width="2"></line><circle cx="70" cy="51" r="2.5" fill="#f6e1bd"></circle></g>
                    <circle cx="50" cy="70" r="3" fill="#f6e1bd"></circle>
                </svg>
            </div>
            <h1 class="text-4xl sm:text-5xl font-extrabold text-white tracking-tight">Firecracker Leles Evaluations</h1>
            <p class="mt-2 text-lg text-gray-400">Player Development & Tryout Registration</p>
        </header>

        <!-- Tab Navigation -->
        <div class="mb-8 flex justify-center bg-gray-800/50 backdrop-blur-sm rounded-xl p-2 shadow-lg border border-gray-700">
            <button class="tab active w-1/2 py-3 px-4 rounded-lg font-semibold text-lg" data-tab="development">Player Development</button>
            <button class="tab w-1/2 py-3 px-4 rounded-lg font-semibold text-lg text-gray-400" data-tab="registration">Tryout Registration</button>
        </div>

        <!-- Tab Content -->
        <main>
            <!-- Player Development Form -->
            <div id="development" class="tab-content active">
                <form id="devForm" class="space-y-8">
                    
                    <!-- Evaluation Context Card -->
                    <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl overflow-hidden reveal">
                        <div class="p-6 border-b border-gray-700">
                            <h2 class="text-2xl font-bold text-white">Evaluation Context</h2>
                        </div>
                        <div class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div>
                                <label for="dev_season" class="block text-sm font-medium text-gray-300 mb-1">Season</label>
                                <select id="dev_season" name="Season" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" required><option value="Fall">Fall</option><option value="Winter">Winter</option><option value="Spring">Spring</option><option value="Summer">Summer</option></select>
                            </div>
                            <div>
                                <label for="dev_year" class="block text-sm font-medium text-gray-300 mb-1">Year</label>
                                <select id="dev_year" name="Year" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" required><option value="24">24</option><option value="25">25</option><option value="26">26</option></select>
                            </div>
                            <div>
                                <label for="dev_age_group" class="block text-sm font-medium text-gray-300 mb-1">Age Group</label>
                                <select id="dev_age_group" name="Age Group" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" required><option value="10u">10u</option><option value="11u">11u</option><option value="12u">12u</option><option value="14u">14u</option></select>
                            </div>
                            <div>
                                <label for="dev_athlete_name" class="block text-sm font-medium text-gray-300 mb-1">Athlete Name</label>
                                <input type="text" id="dev_athlete_name" name="Athlete Name" class="form-input w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" required>
                            </div>
                        </div>
                    </div>

                    <!-- Skills Cards -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Base Running -->
                        <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal">
                            <div class="p-6 flex justify-between items-center">
                                <h3 class="text-xl font-bold text-white">Base Running</h3>
                                <div class="flex items-center"><label class="text-sm font-medium text-gray-400 mr-2 cursor-pointer" for="na_baserunning">Not Applicable</label><input type="checkbox" id="na_baserunning" data-card="baserunning" class="na-checkbox form-checkbox h-5 w-5"></div>
                            </div>
                            <div id="card-baserunning" class="card-content p-6 pt-0 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-1">Home to First Time</label>
                                    <div class="grid grid-cols-3 gap-2">
                                        <input type="text" id="time_1" name="Time 1" placeholder="T1" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric">
                                        <input type="text" id="time_2" name="Time 2" placeholder="T2" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric">
                                        <input type="text" id="time_3" name="Time 3" placeholder="T3" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric">
                                    </div>
                                </div>
                                <div><label for="athleticism_sliding" class="block text-sm font-medium text-gray-300 mb-1">Athleticism/Sliding</label><select id="athleticism_sliding" name="Athleticism/Sliding" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div class="flex items-center pt-2"><input type="checkbox" id="can_slide" name="Can Slide?" class="form-checkbox"><label for="can_slide" class="ml-3 text-sm font-medium text-gray-300">Can Slide?</label></div>
                            </div>
                        </div>

                        <!-- Throwing -->
                        <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal">
                            <div class="p-6 flex justify-between items-center">
                                <h3 class="text-xl font-bold text-white">Throwing</h3>
                                <div class="flex items-center"><label class="text-sm font-medium text-gray-400 mr-2 cursor-pointer" for="na_throwing">Not Applicable</label><input type="checkbox" id="na_throwing" data-card="throwing" class="na-checkbox form-checkbox h-5 w-5"></div>
                            </div>
                            <div id="card-throwing" class="card-content p-6 pt-0 space-y-4">
                                <div><label for="throwing_accuracy" class="block text-sm font-medium text-gray-300 mb-1">Accuracy</label><select id="throwing_accuracy" name="Throwing Accuracy" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div><label for="arm_slot_mechanics" class="block text-sm font-medium text-gray-300 mb-1">Arm Slot/Mechanics</label><select id="arm_slot_mechanics" name="Arm Slot/Mechanics" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-1">Velo</label>
                                    <div class="grid grid-cols-5 gap-2"><input type="text" id="velo_1" name="Velo 1" placeholder="V1" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="velo_2" name="Velo 2" placeholder="V2" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="velo_3" name="Velo 3" placeholder="V3" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="velo_4" name="Velo 4" placeholder="V4" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="velo_5" name="Velo 5" placeholder="V5" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Fielding -->
                        <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal">
                            <div class="p-6 flex justify-between items-center">
                                <h3 class="text-xl font-bold text-white">Fielding</h3>
                                <div class="flex items-center"><label class="text-sm font-medium text-gray-400 mr-2 cursor-pointer" for="na_fielding">Not Applicable</label><input type="checkbox" id="na_fielding" data-card="fielding" class="na-checkbox form-checkbox h-5 w-5"></div>
                            </div>
                            <div id="card-fielding" class="card-content p-6 pt-0 space-y-4">
                                <div><label for="ground_balls" class="block text-sm font-medium text-gray-300 mb-1">Ground Balls</label><select id="ground_balls" name="Ground Balls" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div><label for="fly_balls" class="block text-sm font-medium text-gray-300 mb-1">Fly Balls</label><select id="fly_balls" name="Fly Balls" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                            </div>
                        </div>

                        <!-- Hitting -->
                        <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal">
                            <div class="p-6 flex justify-between items-center">
                                <h3 class="text-xl font-bold text-white">Hitting</h3>
                                <div class="flex items-center"><label class="text-sm font-medium text-gray-400 mr-2 cursor-pointer" for="na_hitting">Not Applicable</label><input type="checkbox" id="na_hitting" data-card="hitting" class="na-checkbox form-checkbox h-5 w-5"></div>
                            </div>
                            <div id="card-hitting" class="card-content p-6 pt-0 space-y-4">
                                <div><label for="hitting_identity" class="block text-sm font-medium text-gray-300 mb-1">Identity</label><select id="hitting_identity" name="Hitting Identity" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"><option>Power</option><option>Contact</option><option>Slapper</option><option>Bunter</option><option>Developing</option></select></div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-1">Bat Speed</label>
                                    <div class="grid grid-cols-3 gap-2"><input type="text" id="speed_1" name="Speed 1" placeholder="S1" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="speed_2" name="Speed 2" placeholder="S2" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="speed_3" name="Speed 3" placeholder="S3" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Pitching -->
                        <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal lg:col-span-2">
                            <div class="p-6 flex justify-between items-center">
                                <h3 class="text-xl font-bold text-white">Pitching</h3>
                                <div class="flex items-center"><label class="text-sm font-medium text-gray-400 mr-2 cursor-pointer" for="na_pitching">Not Applicable</label><input type="checkbox" id="na_pitching" data-card="pitching" class="na-checkbox form-checkbox h-5 w-5"></div>
                            </div>
                            <div id="card-pitching" class="card-content p-6 pt-0 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4">
                                <div><label for="pitching_accuracy" class="block text-sm font-medium text-gray-300 mb-1">Accuracy</label><select id="pitching_accuracy" name="Pitching Accuracy" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div><label for="movement" class="block text-sm font-medium text-gray-300 mb-1">Movement</label><select id="movement" name="Movement" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"><option>Slight</option><option>None</option><option>N/A</option></select></div>
                                <div><label for="money_pitch" class="block text-sm font-medium text-gray-300 mb-1">Money Pitch</label><input type="text" id="money_pitch" name="Money Pitch" class="form-input w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"></div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-1">Velo Fastball</label>
                                    <div class="grid grid-cols-3 gap-2"><input type="text" id="fb_velo_1" name="FB Velo 1" placeholder="V1" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="fb_velo_2" name="FB Velo 2" placeholder="V2" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="fb_velo_3" name="FB Velo 3" placeholder="V3" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-1">Velo Change Up</label>
                                    <div class="grid grid-cols-3 gap-2"><input type="text" id="ch_velo_1" name="CH Velo 1" placeholder="V1" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="ch_velo_2" name="CH Velo 2" placeholder="V2" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="ch_velo_3" name="CH Velo 3" placeholder="V3" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"></div>
                                </div>
                                <div class="md:col-span-2 lg:col-span-1"><label for="velo_notes" class="block text-sm font-medium text-gray-300 mb-1">Velo Notes</label><textarea id="velo_notes" name="Velo Notes" rows="1" class="form-textarea w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"></textarea></div>
                            </div>
                        </div>

                        <!-- Catching -->
                        <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal">
                            <div class="p-6 flex justify-between items-center">
                                <h3 class="text-xl font-bold text-white">Catching</h3>
                                <div class="flex items-center"><label class="text-sm font-medium text-gray-400 mr-2 cursor-pointer" for="na_catching">Not Applicable</label><input type="checkbox" id="na_catching" data-card="catching" class="na-checkbox form-checkbox h-5 w-5"></div>
                            </div>
                            <div id="card-catching" class="card-content p-6 pt-0 space-y-4">
                                <div><label for="receiving" class="block text-sm font-medium text-gray-300 mb-1">Receiving</label><select id="receiving" name="Receiving" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div><label for="blocking" class="block text-sm font-medium text-gray-300 mb-1">Blocking</label><select id="blocking" name="Blocking" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div><label for="catching_throwing_mechanics" class="block text-sm font-medium text-gray-300 mb-1">Throwing Mechanics</label><select id="catching_throwing_mechanics" name="Catching Throwing Mechanics" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white rating-scale"></select></div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-1">Throwing Velocity</label>
                                    <div class="grid grid-cols-3 gap-2"><input type="text" id="catch_velo_1" name="Catch Velo 1" placeholder="V1" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="catch_velo_2" name="Catch Velo 2" placeholder="V2" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"><input type="text" id="catch_velo_3" name="Catch Velo 3" placeholder="V3" class="form-input text-center w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white" inputmode="numeric"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Intangibles -->
                        <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal">
                            <div class="p-6 flex justify-between items-center">
                                <h3 class="text-xl font-bold text-white">Intangibles</h3>
                                <div class="flex items-center"><label class="text-sm font-medium text-gray-400 mr-2 cursor-pointer" for="na_intangibles">Not Applicable</label><input type="checkbox" id="na_intangibles" data-card="intangibles" class="na-checkbox form-checkbox h-5 w-5"></div>
                            </div>
                            <div id="card-intangibles" class="card-content p-6 pt-0 space-y-4">
                                <div><label for="player_attitude_effort" class="block text-sm font-medium text-gray-300 mb-1">Player Attitude/Effort</label><select id="player_attitude_effort" name="Player Attitude/Effort" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"><option>Coachable</option><option>Needs Improvement</option></select></div>
                                <div><label for="projectability" class="block text-sm font-medium text-gray-300 mb-1">Projectability</label><select id="projectability" name="Projectability" class="form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"><option>High</option><option>Medium</option><option>Low</option></select></div>
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Goals Section -->
                    <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl reveal">
                        <div class="p-6 flex justify-between items-center">
                            <h2 class="text-2xl font-bold text-white">Player Goals</h2>
                            <button type="button" id="addGoalBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg transition-transform transform hover:scale-105">+ Add Goal</button>
                        </div>
                        <div id="goalsContainer" class="p-6 pt-0 space-y-6"></div>
                    </div>
                    
                    <!-- Submission -->
                    <div class="pt-5 reveal">
                        <div id="devMessageArea" class="hidden mb-4 p-4 rounded-md text-center"></div>
                        <div class="flex justify-end">
                            <button type="submit" id="devSubmitButton" class="w-full md:w-auto inline-flex justify-center rounded-lg border border-transparent bg-red-600 py-4 px-12 text-lg font-semibold text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-transform transform hover:scale-105">
                                Submit Development Plan
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Tryout Registration Form (Placeholder) -->
            <div id="registration" class="tab-content">
                <div class="bg-gray-800 border border-gray-700 rounded-2xl shadow-xl p-8 text-center">
                    <h2 class="text-2xl font-bold text-white">Tryout Registration</h2>
                    <p class="mt-4 text-gray-400">This form will be built out in the next phase.</p>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Screen Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-gray-900/80 backdrop-blur-sm flex-col items-center justify-center z-50" style="display: none;">
        <div class="w-64 h-64 mx-auto relative">
            <svg viewBox="0 0 100 100" class="w-full h-full">
                <path d="M20,20 L80,20 L80,50 L50,70 L20,50 Z" fill="none" stroke="white" stroke-width="2"></path>
                <line x1="20" y1="20" x2="80" y2="20" stroke="white" stroke-width="2"></line>
                <text x="50" y="20" text-anchor="middle" font-size="9" font-weight="bold" fill="#d1d5db">BASELINE</text>
                <circle cx="20" cy="20" r="3" fill="white"></circle><circle cx="80" cy="20" r="3" fill="white"></circle>
                <circle cx="20" cy="50" r="3" fill="white"></circle><circle cx="80" cy="50" r="3" fill="white"></circle>
                <g><line x1="30" y1="26" x2="30" y2="51" stroke="white" stroke-width="2" class="lineAnimation" style="animation-delay: 0.0s; transform-origin: 30px 51px;"></line><circle cx="30" cy="51" r="2.5" fill="white"></circle></g>
                <g><line x1="40" y1="32" x2="40" y2="57" stroke="white" stroke-width="2" class="lineAnimation" style="animation-delay: 0.2s; transform-origin: 40px 57px;"></line><circle cx="40" cy="57" r="2.5" fill="white"></circle></g>
                <g><line x1="50" y1="36" x2="50" y2="61" stroke="white" stroke-width="2" class="lineAnimation" style="animation-delay: 0.4s; transform-origin: 50px 61px;"></line><circle cx="50" cy="61" r="2.5" fill="white"></circle></g>
                <g><line x1="60" y1="32" x2="60" y2="57" stroke="white" stroke-width="2" class="lineAnimation" style="animation-delay: 0.6s; transform-origin: 60px 57px;"></line><circle cx="60" cy="57" r="2.5" fill="white"></circle></g>
                <g><line x1="70" y1="26" x2="70" y2="51" stroke="white" stroke-width="2" class="lineAnimation" style="animation-delay: 0.8s; transform-origin: 70px 51px;"></line><circle cx="70" cy="51" r="2.5" fill="white"></circle></g>
                <circle cx="50" cy="70" r="3" fill="white"></circle>
            </svg>
        </div>
        <p class="mt-4 text-lg font-semibold text-white">Submitting Data...</p>
    </div>

    <!-- Template for Goal Entry -->
    <template id="goalTemplate">
        <div class="goal-entry bg-gray-900/50 p-4 rounded-lg border border-gray-700 space-y-4 relative">
            <button type="button" class="remove-goal-btn absolute -top-3 -right-3 bg-red-500 text-white rounded-full h-7 w-7 flex items-center justify-center font-bold text-sm">&times;</button>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="goal-season-label block text-sm font-medium text-gray-300 mb-1">Goal Season</label>
                    <select class="goal-season form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"><option value="Fall">Fall</option><option value="Winter">Winter</option><option value="Spring">Spring</option><option value="Summer">Summer</option></select>
                </div>
                 <div>
                    <label class="goal-year-label block text-sm font-medium text-gray-300 mb-1">Goal Year</label>
                    <select class="goal-year form-select w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"><option value="24">24</option><option value="25">25</option><option value="26">26</option></select>
                </div>
                <div class="md:col-span-3 lg:col-span-1">
                    <label class="goal-categories-label block text-sm font-medium text-gray-300 mb-1">Goal Categories</label>
                    <div class="p-2 bg-gray-700 border-gray-600 rounded-md max-h-32 overflow-y-auto">
                        <div class="goal-categories grid grid-cols-2 gap-2"></div>
                    </div>
                </div>
            </div>
            <div>
                <label class="goal-note-label block text-sm font-medium text-gray-300 mb-1">Goal Note</label>
                <textarea rows="2" class="goal-note form-textarea w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white"></textarea>
            </div>
        </div>
    </template>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        // --- CONFIGURATION ---
        const SCRIPT_URL = "https://script.google.com/macros/s/AKfycbwxMCOzgc2uGq1-d1odB-8Ms1QMYv3jMYt1FXWgWHgdJgXxwxfXMtuER6SBjQ7W4xP8LA/exec";
        
        // --- RATING SCALE LOGIC ---
        const ratingScaleOptions = [
            { value: 1, text: '1 - Needs significant improvement' }, { value: 2, text: '2' },
            { value: 3, text: '3 - Developing, Inconsistent' },    { value: 4, text: '4' },
            { value: 5, text: '5 - Average, Meets Expectations' }, { value: 6, text: '6' },
            { value: 7, text: '7 - Above Average, Consistent' },   { value: 8, text: '8' },
            { value: 9, text: '9' },                               { value: 10, text: '10 - Exceptional, Elite' }
        ];
        document.querySelectorAll('.rating-scale').forEach(select => {
            ratingScaleOptions.forEach(opt => {
                const option = document.createElement('option');
                option.value = opt.value;
                option.textContent = opt.text;
                select.appendChild(option);
            });
        });

        // --- TAB SWITCHING LOGIC ---
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const target = tab.dataset.tab;
                tabs.forEach(t => {
                    t.classList.remove('active');
                    t.classList.add('text-gray-400');
                });
                tab.classList.add('active');
                tab.classList.remove('text-gray-400');
                tabContents.forEach(content => {
                    if (content.id === target) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
            });
        });

        // --- SCROLL ANIMATION LOGIC ---
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) entry.target.classList.add('visible');
            });
        }, { threshold: 0.1 });
        document.querySelectorAll('.reveal').forEach(el => observer.observe(el));
        
        // --- NOT APPLICABLE CHECKBOX LOGIC ---
        document.querySelectorAll('.na-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const cardId = e.target.dataset.card;
                const contentArea = document.getElementById(`card-${cardId}`);
                contentArea.classList.toggle('disabled', e.target.checked);
            });
        });

        // --- DYNAMIC GOALS LOGIC ---
        const addGoalBtn = document.getElementById('addGoalBtn');
        const goalsContainer = document.getElementById('goalsContainer');
        const goalTemplate = document.getElementById('goalTemplate');
        const maxGoals = 3;
        const goalCategories = [
            'Base Running - Time', 'Base Running - Athleticism', 'Throwing - Accuracy', 'Throwing - Arm Slot', 'Throwing - Velo',
            'Fielding - Ground Balls', 'Fielding - Fly Balls', 'Hitting - Identity', 'Hitting - Bat Speed', 'Pitching - Accuracy',
            'Pitching - Velo FB', 'Pitching - Velo CH', 'Pitching - Movement', 'Pitching - Money Pitch', 'Catching - Receiving',
            'Catching - Blocking', 'Catching - Mechanics', 'Catching - Velo', 'Attitude/Effort', 'Projectability'
        ];
        const updateAddButtonState = () => {
            const currentGoals = goalsContainer.querySelectorAll('.goal-entry').length;
            addGoalBtn.disabled = currentGoals >= maxGoals;
            addGoalBtn.classList.toggle('opacity-50', currentGoals >= maxGoals);
            addGoalBtn.classList.toggle('cursor-not-allowed', currentGoals >= maxGoals);
        };
        addGoalBtn.addEventListener('click', () => {
            if (goalsContainer.querySelectorAll('.goal-entry').length >= maxGoals) return;
            const newGoal = goalTemplate.content.cloneNode(true);
            const goalIndex = goalsContainer.querySelectorAll('.goal-entry').length;

            // Set up proper IDs and labels for the main goal fields
            const goalSeasonSelect = newGoal.querySelector('.goal-season');
            const goalSeasonLabel = newGoal.querySelector('.goal-season-label');
            const goalYearSelect = newGoal.querySelector('.goal-year');
            const goalYearLabel = newGoal.querySelector('.goal-year-label');
            const goalNote = newGoal.querySelector('.goal-note');
            const goalNoteLabel = newGoal.querySelector('.goal-note-label');
            const goalCategoriesLabel = newGoal.querySelector('.goal-categories-label');

            const goalSeasonId = `goal-${goalIndex}-season`;
            const goalYearId = `goal-${goalIndex}-year`;
            const goalNoteId = `goal-${goalIndex}-note`;
            const goalCategoriesId = `goal-${goalIndex}-categories`;

            goalSeasonSelect.id = goalSeasonId;
            goalSeasonSelect.name = `goal_${goalIndex}_season`;
            goalSeasonLabel.setAttribute('for', goalSeasonId);

            goalYearSelect.id = goalYearId;
            goalYearSelect.name = `goal_${goalIndex}_year`;
            goalYearLabel.setAttribute('for', goalYearId);

            goalNote.id = goalNoteId;
            goalNote.name = `goal_${goalIndex}_note`;
            goalNoteLabel.setAttribute('for', goalNoteId);

            goalCategoriesLabel.setAttribute('for', goalCategoriesId);

            const categoriesContainer = newGoal.querySelector('.goal-categories');
            goalCategories.forEach((cat, catIndex) => {
                const div = document.createElement('div');
                div.className = 'flex items-center';
                const input = document.createElement('input');
                input.type = 'checkbox';
                input.id = `goal-${goalIndex}-cat-${catIndex}`;
                input.name = `goal_${goalIndex}_categories`;
                input.value = cat;
                input.className = 'form-checkbox h-4 w-4';
                const label = document.createElement('label');
                label.htmlFor = input.id;
                label.textContent = cat;
                label.className = 'ml-2 text-sm text-gray-300';
                div.appendChild(input);
                div.appendChild(label);
                categoriesContainer.appendChild(div);
            });
            newGoal.querySelector('.remove-goal-btn').addEventListener('click', (e) => {
                e.target.closest('.goal-entry').remove();
                updateAddButtonState();
            });
            goalsContainer.appendChild(newGoal);
            updateAddButtonState();
        });
        updateAddButtonState();

        // --- FORM SUBMISSION LOGIC (FIXED FOR FORMDATA) ---
        const devForm = document.getElementById('devForm');
        const devSubmitButton = document.getElementById('devSubmitButton');
        const devMessageArea = document.getElementById('devMessageArea');
        const loadingOverlay = document.getElementById('loadingOverlay');

        devForm.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Show loading screen
            loadingOverlay.style.display = 'flex';
            devSubmitButton.disabled = true;

            // --- Create FormData object ---
            const formData = new FormData();
            
            // Add form type
            formData.append('formType', 'development');

            // --- Process regular form fields ---
            const formElements = devForm.elements;
            for (let element of formElements) {
                if (!element.name) continue; // Skip elements without names
                
                // Skip NA checkboxes and goal fields, we'll handle them separately
                if (element.name.startsWith('na_') || element.name.startsWith('goal_')) continue;
                
                if (element.type === 'checkbox') {
                    if (element.name === 'Can Slide?') {
                        formData.append(element.name, element.checked);
                    }
                } else if (element.value) {
                    formData.append(element.name, element.value);
                }
            }

            // Combine season and year
            const season = formData.get('Season');
            const year = formData.get('Year');
            if (season && year) {
                formData.set('Season', `${season} ${year}`);
                formData.delete('Year');
            }

            // --- Process Goals ---
            const goalEntries = goalsContainer.querySelectorAll('.goal-entry');
            goalEntries.forEach((goal, index) => {
                const goalNum = index + 1;
                const season = goal.querySelector('.goal-season').value;
                const year = goal.querySelector('.goal-year').value;
                formData.append(`Goal ${goalNum} Season`, `${season} ${year}`);
                
                const categories = [];
                goal.querySelectorAll('.goal-categories input:checked').forEach(cat => {
                    categories.push(cat.value);
                });
                formData.append(`Goal ${goalNum} Categories`, categories.join(', '));

                const goalNote = goal.querySelector('.goal-note').value;
                if (goalNote) {
                    formData.append(`Goal ${goalNum} Note`, goalNote);
                }
            });

            // --- Send FormData to Google Apps Script (CORS-Free Method) ---
            
            // Create a hidden iframe to submit the form without page redirect
            const iframe = document.createElement('iframe');
            iframe.name = 'hidden_iframe';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            // Create a temporary form for submission
            const tempForm = document.createElement('form');
            tempForm.method = 'POST';
            tempForm.action = SCRIPT_URL;
            tempForm.target = 'hidden_iframe';
            
            // Add all FormData entries to the temporary form
            for (let [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                tempForm.appendChild(input);
            }
            
            document.body.appendChild(tempForm);
            
            // Submit the form
            tempForm.submit();
            
            // Clean up and show success (since we can't read the response with this method)
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
                devSubmitButton.disabled = false;
                devMessageArea.textContent = 'Form submitted! Please check your Google Sheet to confirm the data was recorded.';
                devMessageArea.className = 'mb-4 p-4 rounded-md text-center bg-green-900/50 text-green-300 border border-green-700';
                devMessageArea.classList.remove('hidden');
                
                // Reset form
                devForm.reset();
                goalsContainer.innerHTML = '';
                updateAddButtonState();
                document.querySelectorAll('.card-content').forEach(c => c.classList.remove('disabled'));
                
                // Clean up temporary elements
                document.body.removeChild(tempForm);
                document.body.removeChild(iframe);
                
                window.scrollTo(0, document.body.scrollHeight);
            }, 2000); // 2 second delay to allow form submission
        });
    });
    </script>
</body>
</html>